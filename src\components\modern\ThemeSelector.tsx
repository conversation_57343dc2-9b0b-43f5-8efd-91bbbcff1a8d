import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { modernAnimations } from '@/lib/animations';
import { advancedThemes, applyTheme, type AdvancedTheme } from '@/lib/advanced-themes';
import { InteractiveCard } from './InteractiveCard';
import { AdvancedButton } from './AdvancedButton';
import { 
  Palette, 
  Monitor, 
  Smartphone, 
  Eye, 
  Sparkles,
  Check,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ThemeSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  currentTheme?: string;
  onThemeChange?: (themeId: string) => void;
}

export const ThemeSelector: React.FC<ThemeSelectorProps> = ({
  isOpen,
  onClose,
  currentTheme = 'glassmorphism',
  onThemeChange
}) => {
  const [selectedTheme, setSelectedTheme] = useState(currentTheme);
  const [previewMode, setPreviewMode] = useState<'desktop' | 'mobile'>('desktop');

  const handleThemeSelect = (themeId: string) => {
    setSelectedTheme(themeId);
    applyTheme(themeId);
    onThemeChange?.(themeId);
  };

  const handlePreview = (themeId: string) => {
    // Temporarily apply theme for preview
    applyTheme(themeId);
  };

  const handlePreviewEnd = () => {
    // Restore selected theme
    applyTheme(selectedTheme);
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
          variants={modernAnimations.advancedModal.backdrop}
          initial="initial"
          animate="animate"
          exit="exit"
        >
          {/* Backdrop */}
          <div 
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={onClose}
          />

          {/* Modal Content */}
          <motion.div
            className="relative w-full max-w-6xl max-h-[90vh] overflow-hidden"
            variants={modernAnimations.advancedModal.content}
            initial="initial"
            animate="animate"
            exit="exit"
          >
            <InteractiveCard 
              variant="glass" 
              className="h-full overflow-y-auto"
              interactive={false}
            >
              {/* Header */}
              <div className="flex items-center justify-between mb-8">
                <div className="flex items-center gap-3">
                  <motion.div
                    className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl text-white"
                    whileHover={{ rotate: 360 }}
                    transition={{ duration: 0.5 }}
                  >
                    <Palette className="w-6 h-6" />
                  </motion.div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900">Theme Selector</h2>
                    <p className="text-gray-600">Choose your perfect interface style</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  {/* Preview Mode Toggle */}
                  <div className="flex bg-gray-100 rounded-lg p-1">
                    <button
                      className={cn(
                        'flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-all',
                        previewMode === 'desktop' 
                          ? 'bg-white text-gray-900 shadow-sm' 
                          : 'text-gray-600 hover:text-gray-900'
                      )}
                      onClick={() => setPreviewMode('desktop')}
                    >
                      <Monitor className="w-4 h-4" />
                      Desktop
                    </button>
                    <button
                      className={cn(
                        'flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-all',
                        previewMode === 'mobile' 
                          ? 'bg-white text-gray-900 shadow-sm' 
                          : 'text-gray-600 hover:text-gray-900'
                      )}
                      onClick={() => setPreviewMode('mobile')}
                    >
                      <Smartphone className="w-4 h-4" />
                      Mobile
                    </button>
                  </div>

                  <AdvancedButton variant="ghost" onClick={onClose}>
                    Close
                  </AdvancedButton>
                </div>
              </div>

              {/* Theme Grid */}
              <motion.div 
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 gap-6"
                variants={modernAnimations.waveGrid}
                initial="initial"
                animate="animate"
              >
                {Object.entries(advancedThemes).map(([themeId, theme], index) => (
                  <motion.div
                    key={themeId}
                    variants={modernAnimations.waveGridItem}
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    <ThemeCard
                      theme={theme}
                      isSelected={selectedTheme === themeId}
                      onSelect={() => handleThemeSelect(themeId)}
                      onPreview={() => handlePreview(themeId)}
                      onPreviewEnd={handlePreviewEnd}
                      previewMode={previewMode}
                    />
                  </motion.div>
                ))}
              </motion.div>

              {/* Custom Theme Section */}
              <motion.div
                className="mt-12 p-6 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Settings className="w-5 h-5 text-gray-600" />
                    <div>
                      <h3 className="font-semibold text-gray-900">Custom Theme</h3>
                      <p className="text-sm text-gray-600">Create your own unique theme</p>
                    </div>
                  </div>
                  <AdvancedButton variant="gradient" size="sm">
                    Coming Soon
                  </AdvancedButton>
                </div>
              </motion.div>
            </InteractiveCard>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

interface ThemeCardProps {
  theme: AdvancedTheme;
  isSelected: boolean;
  onSelect: () => void;
  onPreview: () => void;
  onPreviewEnd: () => void;
  previewMode: 'desktop' | 'mobile';
}

const ThemeCard: React.FC<ThemeCardProps> = ({
  theme,
  isSelected,
  onSelect,
  onPreview,
  onPreviewEnd,
  previewMode
}) => {
  return (
    <motion.div
      className={cn(
        'relative cursor-pointer transition-all duration-300',
        isSelected && 'ring-2 ring-blue-400 ring-offset-2'
      )}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onMouseEnter={onPreview}
      onMouseLeave={onPreviewEnd}
      onClick={onSelect}
    >
      <InteractiveCard 
        variant="default" 
        className="h-full overflow-hidden"
        interactive={false}
      >
        {/* Theme Preview */}
        <div 
          className="h-32 rounded-lg mb-4 relative overflow-hidden"
          style={{ 
            background: theme.colors.background,
            border: `1px solid ${theme.colors.primary}`
          }}
        >
          {/* Mini UI Elements */}
          <div className="absolute top-2 left-2 right-2">
            <div 
              className="h-2 rounded-full mb-2"
              style={{ background: theme.colors.primary }}
            />
            <div className="flex gap-1">
              <div 
                className="h-1 w-8 rounded"
                style={{ background: theme.colors.secondary }}
              />
              <div 
                className="h-1 w-6 rounded"
                style={{ background: theme.colors.accent }}
              />
            </div>
          </div>

          <div className="absolute bottom-2 left-2 right-2">
            <div 
              className="h-6 rounded mb-1"
              style={{ 
                background: theme.effects.glassmorphism.background,
                backdropFilter: theme.effects.glassmorphism.backdropFilter,
                border: theme.effects.glassmorphism.border
              }}
            />
          </div>

          {/* Floating Elements */}
          <motion.div
            className="absolute top-4 right-4 w-3 h-3 rounded-full"
            style={{ background: theme.colors.accent }}
            animate={{ y: [0, -5, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
          />
        </div>

        {/* Theme Info */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-gray-900">{theme.name}</h3>
            {isSelected && (
              <motion.div
                className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", damping: 15 }}
              >
                <Check className="w-3 h-3 text-white" />
              </motion.div>
            )}
          </div>
          <p className="text-sm text-gray-600">{theme.description}</p>
          
          {/* Color Palette */}
          <div className="flex gap-1 mt-3">
            <div 
              className="w-4 h-4 rounded-full border border-gray-200"
              style={{ background: theme.colors.primary }}
            />
            <div 
              className="w-4 h-4 rounded-full border border-gray-200"
              style={{ background: theme.colors.secondary }}
            />
            <div 
              className="w-4 h-4 rounded-full border border-gray-200"
              style={{ background: theme.colors.accent }}
            />
          </div>
        </div>

        {/* Hover Effect */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent opacity-0 pointer-events-none"
          whileHover={{ opacity: 1 }}
          transition={{ duration: 0.2 }}
        />
      </InteractiveCard>
    </motion.div>
  );
};
