import React, { useState, useRef } from 'react';
import { motion, useDragControls, Reorder } from 'framer-motion';
import { modernAnimations } from '@/lib/animations';
import { InteractiveCard } from './InteractiveCard';
import { AdvancedButton } from './AdvancedButton';
import { 
  Layout, 
  Type, 
  Image, 
  Button, 
  Square, 
  Circle,
  GripVertical,
  Plus,
  Trash2,
  Eye,
  Code,
  Save
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ComponentItem {
  id: string;
  type: 'text' | 'button' | 'image' | 'card' | 'input';
  label: string;
  icon: React.ReactNode;
  props: Record<string, any>;
}

interface DroppedComponent extends ComponentItem {
  position: { x: number; y: number };
  size: { width: number; height: number };
}

const componentLibrary: ComponentItem[] = [
  {
    id: 'text',
    type: 'text',
    label: 'Text',
    icon: <Type className="w-4 h-4" />,
    props: { content: 'Sample Text', fontSize: '16px', color: '#000000' }
  },
  {
    id: 'button',
    type: 'button',
    label: 'Button',
    icon: <Button className="w-4 h-4" />,
    props: { text: 'Click Me', variant: 'primary', size: 'md' }
  },
  {
    id: 'image',
    type: 'image',
    label: 'Image',
    icon: <Image className="w-4 h-4" />,
    props: { src: '/placeholder.svg', alt: 'Image', width: '200px', height: '150px' }
  },
  {
    id: 'card',
    type: 'card',
    label: 'Card',
    icon: <Square className="w-4 h-4" />,
    props: { title: 'Card Title', content: 'Card content goes here' }
  }
];

export const DragDropBuilder: React.FC = () => {
  const [droppedComponents, setDroppedComponents] = useState<DroppedComponent[]>([]);
  const [selectedComponent, setSelectedComponent] = useState<string | null>(null);
  const [mode, setMode] = useState<'design' | 'preview' | 'code'>('design');
  const canvasRef = useRef<HTMLDivElement>(null);

  const handleDrop = (event: React.DragEvent, componentType: ComponentItem) => {
    event.preventDefault();
    
    if (!canvasRef.current) return;

    const rect = canvasRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    const newComponent: DroppedComponent = {
      ...componentType,
      id: `${componentType.type}-${Date.now()}`,
      position: { x, y },
      size: { width: 200, height: 100 }
    };

    setDroppedComponents(prev => [...prev, newComponent]);
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
  };

  const updateComponentPosition = (id: string, position: { x: number; y: number }) => {
    setDroppedComponents(prev =>
      prev.map(comp => comp.id === id ? { ...comp, position } : comp)
    );
  };

  const deleteComponent = (id: string) => {
    setDroppedComponents(prev => prev.filter(comp => comp.id !== id));
    setSelectedComponent(null);
  };

  const generateCode = () => {
    return `
// Generated React Component
import React from 'react';

export const GeneratedComponent = () => {
  return (
    <div className="relative w-full h-full">
      ${droppedComponents.map(comp => {
        switch (comp.type) {
          case 'text':
            return `
      <div 
        style={{ 
          position: 'absolute', 
          left: '${comp.position.x}px', 
          top: '${comp.position.y}px',
          fontSize: '${comp.props.fontSize}',
          color: '${comp.props.color}'
        }}
      >
        ${comp.props.content}
      </div>`;
          case 'button':
            return `
      <button 
        style={{ 
          position: 'absolute', 
          left: '${comp.position.x}px', 
          top: '${comp.position.y}px'
        }}
        className="px-4 py-2 bg-blue-500 text-white rounded"
      >
        ${comp.props.text}
      </button>`;
          default:
            return '';
        }
      }).join('')}
    </div>
  );
};`;
  };

  return (
    <div className="h-screen flex bg-gray-50">
      {/* Component Library Sidebar */}
      <motion.div
        className="w-80 bg-white border-r border-gray-200 p-6"
        initial={{ x: -300 }}
        animate={{ x: 0 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
      >
        <div className="mb-6">
          <h2 className="text-xl font-bold text-gray-900 mb-2">Components</h2>
          <p className="text-sm text-gray-600">Drag components to the canvas</p>
        </div>

        <div className="space-y-3">
          {componentLibrary.map((component, index) => (
            <motion.div
              key={component.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <ComponentLibraryItem 
                component={component}
                onDrop={handleDrop}
              />
            </motion.div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="mt-8 space-y-3">
          <AdvancedButton 
            variant="gradient" 
            className="w-full"
            icon={<Save className="w-4 h-4" />}
          >
            Save Layout
          </AdvancedButton>
          <AdvancedButton 
            variant="ghost" 
            className="w-full"
            icon={<Plus className="w-4 h-4" />}
          >
            Add Custom Component
          </AdvancedButton>
        </div>
      </motion.div>

      {/* Main Canvas Area */}
      <div className="flex-1 flex flex-col">
        {/* Toolbar */}
        <div className="bg-white border-b border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="flex bg-gray-100 rounded-lg p-1">
                {(['design', 'preview', 'code'] as const).map((modeOption) => (
                  <button
                    key={modeOption}
                    className={cn(
                      'px-3 py-2 rounded-md text-sm font-medium transition-all capitalize',
                      mode === modeOption 
                        ? 'bg-white text-gray-900 shadow-sm' 
                        : 'text-gray-600 hover:text-gray-900'
                    )}
                    onClick={() => setMode(modeOption)}
                  >
                    {modeOption === 'design' && <Layout className="w-4 h-4 mr-2" />}
                    {modeOption === 'preview' && <Eye className="w-4 h-4 mr-2" />}
                    {modeOption === 'code' && <Code className="w-4 h-4 mr-2" />}
                    {modeOption}
                  </button>
                ))}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">
                {droppedComponents.length} components
              </span>
              <AdvancedButton variant="primary" size="sm">
                Export
              </AdvancedButton>
            </div>
          </div>
        </div>

        {/* Canvas */}
        <div className="flex-1 p-6">
          {mode === 'design' && (
            <motion.div
              ref={canvasRef}
              className="relative w-full h-full bg-white rounded-lg border-2 border-dashed border-gray-300 overflow-hidden"
              onDrop={(e) => {
                // Handle drop from library
                const componentData = e.dataTransfer.getData('component');
                if (componentData) {
                  const component = JSON.parse(componentData);
                  handleDrop(e, component);
                }
              }}
              onDragOver={handleDragOver}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              {droppedComponents.length === 0 && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <Layout className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 text-lg font-medium">Drop components here</p>
                    <p className="text-gray-400 text-sm">Start building your interface</p>
                  </div>
                </div>
              )}

              {droppedComponents.map((component) => (
                <DraggableComponent
                  key={component.id}
                  component={component}
                  isSelected={selectedComponent === component.id}
                  onSelect={() => setSelectedComponent(component.id)}
                  onPositionChange={(position) => updateComponentPosition(component.id, position)}
                  onDelete={() => deleteComponent(component.id)}
                />
              ))}
            </motion.div>
          )}

          {mode === 'preview' && (
            <motion.div
              className="w-full h-full bg-white rounded-lg border border-gray-200 overflow-hidden"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
            >
              <div className="relative w-full h-full">
                {droppedComponents.map((component) => (
                  <ComponentRenderer key={component.id} component={component} />
                ))}
              </div>
            </motion.div>
          )}

          {mode === 'code' && (
            <motion.div
              className="w-full h-full bg-gray-900 rounded-lg p-6 overflow-auto"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
            >
              <pre className="text-green-400 text-sm font-mono">
                {generateCode()}
              </pre>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
};

// Component Library Item
interface ComponentLibraryItemProps {
  component: ComponentItem;
  onDrop: (event: React.DragEvent, component: ComponentItem) => void;
}

const ComponentLibraryItem: React.FC<ComponentLibraryItemProps> = ({ component, onDrop }) => {
  return (
    <motion.div
      className="p-3 bg-gray-50 rounded-lg border border-gray-200 cursor-grab active:cursor-grabbing hover:bg-gray-100 transition-colors"
      draggable
      onDragStart={(e) => {
        e.dataTransfer.setData('component', JSON.stringify(component));
      }}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      <div className="flex items-center gap-3">
        <div className="p-2 bg-white rounded-md border">
          {component.icon}
        </div>
        <div>
          <p className="font-medium text-gray-900">{component.label}</p>
          <p className="text-xs text-gray-500 capitalize">{component.type} component</p>
        </div>
      </div>
    </motion.div>
  );
};

// Draggable Component on Canvas
interface DraggableComponentProps {
  component: DroppedComponent;
  isSelected: boolean;
  onSelect: () => void;
  onPositionChange: (position: { x: number; y: number }) => void;
  onDelete: () => void;
}

const DraggableComponent: React.FC<DraggableComponentProps> = ({
  component,
  isSelected,
  onSelect,
  onPositionChange,
  onDelete
}) => {
  const dragControls = useDragControls();

  return (
    <motion.div
      className={cn(
        'absolute cursor-pointer group',
        isSelected && 'ring-2 ring-blue-400'
      )}
      style={{
        left: component.position.x,
        top: component.position.y,
        width: component.size.width,
        height: component.size.height
      }}
      drag
      dragControls={dragControls}
      dragMomentum={false}
      onDrag={(_, info) => {
        onPositionChange({
          x: component.position.x + info.delta.x,
          y: component.position.y + info.delta.y
        });
      }}
      onClick={onSelect}
      whileHover={{ scale: 1.02 }}
    >
      <ComponentRenderer component={component} />
      
      {/* Selection Controls */}
      {isSelected && (
        <motion.div
          className="absolute -top-8 left-0 flex items-center gap-1"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <button
            className="p-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600"
            onPointerDown={(e) => dragControls.start(e)}
          >
            <GripVertical className="w-3 h-3" />
          </button>
          <button
            className="p-1 bg-red-500 text-white rounded text-xs hover:bg-red-600"
            onClick={onDelete}
          >
            <Trash2 className="w-3 h-3" />
          </button>
        </motion.div>
      )}
    </motion.div>
  );
};

// Component Renderer
interface ComponentRendererProps {
  component: DroppedComponent;
}

const ComponentRenderer: React.FC<ComponentRendererProps> = ({ component }) => {
  switch (component.type) {
    case 'text':
      return (
        <div 
          style={{ 
            fontSize: component.props.fontSize,
            color: component.props.color
          }}
          className="p-2"
        >
          {component.props.content}
        </div>
      );
    case 'button':
      return (
        <button className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
          {component.props.text}
        </button>
      );
    case 'card':
      return (
        <div className="p-4 bg-white border border-gray-200 rounded-lg shadow-sm h-full">
          <h3 className="font-semibold text-gray-900 mb-2">{component.props.title}</h3>
          <p className="text-gray-600 text-sm">{component.props.content}</p>
        </div>
      );
    default:
      return <div className="p-2 bg-gray-100 rounded">Unknown Component</div>;
  }
};
