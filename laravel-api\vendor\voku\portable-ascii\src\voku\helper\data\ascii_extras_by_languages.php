<?php

return [
    // Dutch (Flemish)
    'nl' => [
        '=' => ' gelijk ',
        '%' => ' procent ',
        '∑' => ' som ',
        '∆' => ' delta ',
        '∞' => ' oneindig ',
        '♥' => ' love ',
        '&' => ' en ',
        '+' => ' plus ',
    ],
    // Italian
    'it' => [
        '=' => ' uguale ',
        '%' => ' percent ',
        '∑' => ' somma ',
        '∆' => ' delta ',
        '∞' => ' infinito ',
        '♥' => ' amore ',
        '&' => ' e ',
        '+' => ' piu ',
    ],
    // Macedonian
    'mk' => [
        '=' => ' ednakva ',
        '%' => ' procenti ',
        '∑' => ' zbir ',
        '∆' => ' delta ',
        '∞' => ' beskonecnost ',
        '♥' => ' loveubov ',
        '&' => ' i ',
        '+' => ' plus ',
    ],
    // Portuguese (Brazil)
    'pt' => [
        '=' => ' igual ',
        '%' => ' por cento ',
        '∑' => ' soma ',
        '∆' => ' delta ',
        '∞' => ' infinito ',
        '♥' => ' amor ',
        '&' => ' e ',
        '+' => ' mais ',
    ],
    // Greek(lish) (Elláda)
    'el__greeklish' => [
        '=' => ' isos ',
        '%' => ' tois ekato ',
        '∑' => ' athroisma ',
        '∆' => ' delta ',
        '∞' => ' apeiro ',
        '♥' => ' agape ',
        '&' => ' kai ',
        '+' => ' syn ',
    ],
    // Greek (Elláda)
    'el' => [
        '=' => ' isos ',
        '%' => ' tois ekato ',
        '∑' => ' athroisma ',
        '∆' => ' delta ',
        '∞' => ' apeiro ',
        '♥' => ' agape ',
        '&' => ' kai ',
        '+' => ' syn ',
    ],
    // Hindi
    'hi' => [
        '=' => ' samana ',
        '%' => ' paratisata ',
        '∑' => ' yoga ',
        '∆' => ' dalata ',
        '∞' => ' anata ',
        '♥' => ' payara ',
        '&' => ' aura ',
        '+' => ' palasa ',
    ],
    // Armenian
    'hy' => [
        '=' => ' havasar ',
        '%' => ' tvokvos ',
        '∑' => ' gvoumar ',
        '∆' => ' delta ',
        '∞' => ' ansahmanvouthyvoun ',
        '♥' => ' ser ',
        '&' => ' ev ',
        '+' => ' gvoumarats ',
    ],
    // Swedish
    'sv' => [
        '=' => ' lika ',
        '%' => ' procent ',
        '∑' => ' summa ',
        '∆' => ' delta ',
        '∞' => ' oandlighet ',
        '♥' => ' alskar ',
        '&' => ' och ',
        '+' => ' plus ',
    ],
    // Turkmen
    'tk' => [
        '=' => ' den ',
        '%' => ' yuzde ',
        '∑' => ' jem ',
        '∆' => ' delta ',
        '∞' => ' mudimilik ',
        '♥' => ' soygi ',
        '&' => ' we ',
        '+' => ' yzy ',
    ],
    // Turkish
    'tr' => [
        '=' => ' esit ',
        '%' => ' yuzde ',
        '∑' => ' Toplam ',
        '∆' => ' delta ',
        '∞' => ' sonsuzluk ',
        '♥' => ' ask ',
        '&' => ' ve ',
        '+' => ' arti ',
    ],
    // Bulgarian
    'bg' => [
        '=' => ' raven ',
        '%' => ' na sto ',
        '∑' => ' suma ',
        '∆' => ' delta ',
        '∞' => ' bezkrajnost ',
        '♥' => ' obicam ',
        '&' => ' i ',
        '+' => ' plus ',
    ],
    // Hungarian
    'hu' => [
        '=' => ' Egyenlo ',
        '%' => ' Szazalek ',
        '∑' => ' osszeg ',
        '∆' => ' delta ',
        '∞' => ' vegtelenitett ',
        '♥' => ' love ',
        '&' => ' Es ',
        '+' => ' Plusz ',
    ],
    // Myanmar (Burmese)
    'my' => [
        '=' => ' ttn:ttnnym? ',
        '%' => ' raakhngnn:k ',
        '∑' => ' ld ',
        '∆' => ' m?cwk?n:pe? ',
        '∞' => ' ach:m ',
        '♥' => ' mettttaa ',
        '&' => ' n ',
        '+' => ' ape?ng: ',
    ],
    // Croatian (Hrvatska)
    'hr' => [
        '=' => ' Jednaki ',
        '%' => ' Posto ',
        '∑' => ' zbroj ',
        '∆' => ' Delta ',
        '∞' => ' beskonacno ',
        '♥' => ' ljubav ',
        '&' => ' I ',
        '+' => ' Plus ',
    ],
    // Finnish
    'fi' => [
        '=' => ' Sama ',
        '%' => ' Prosenttia ',
        '∑' => ' sum ',
        '∆' => ' delta ',
        '∞' => ' aareton ',
        '♥' => ' rakkautta ',
        '&' => ' Ja ',
        '+' => ' Plus ',
    ],
    // Georgian (Kartvelian)
    'ka' => [
        '=' => ' tanasts\'ori ',
        '%' => ' p\'rotsent\'i ',
        '∑' => ' tankha ',
        '∆' => ' delt\'a ',
        '∞' => ' usasrulo ',
        '♥' => ' siq\'varuli ',
        '&' => ' da ',
        '+' => ' p\'lus ',
    ],
    // Russian
    'ru' => [
        '=' => ' ravnyj ',
        '%' => ' procent ',
        '∑' => ' summa ',
        '∆' => ' del\'ta ',
        '∞' => ' beskonecnost\' ',
        '♥' => ' lublu ',
        '&' => ' i ',
        '+' => ' plus ',
    ],
    // Russian - GOST 7.79-2000(B)
    'ru__gost_2000_b' => [
        '=' => ' ravnyj ',
        '%' => ' procent ',
        '∑' => ' summa ',
        '∆' => ' del\'ta ',
        '∞' => ' beskonecnost\' ',
        '♥' => ' lublu ',
        '&' => ' i ',
        '+' => ' plus ',
    ],
    // Russian - Passport (2013), ICAO
    'ru__passport_2013' => [
        '=' => ' ravnyj ',
        '%' => ' procent ',
        '∑' => ' summa ',
        '∆' => ' del\'ta ',
        '∞' => ' beskonecnost\' ',
        '♥' => ' lublu ',
        '&' => ' i ',
        '+' => ' plus ',
    ],
    // Ukrainian
    'uk' => [
        '=' => ' rivnij ',
        '%' => ' vidsotkiv ',
        '∑' => ' suma ',
        '∆' => ' del\'ta ',
        '∞' => ' neskincennist\' ',
        '♥' => ' lubov ',
        '&' => ' i ',
        '+' => ' plus ',
    ],
    // Kazakh
    'kk' => [
        '=' => ' ten\' ',
        '%' => ' Pajyzdar ',
        '∑' => ' zalpy ',
        '∆' => ' ajyrmasylyk, ',
        '∞' => ' seksiz ',
        '♥' => ' mahabbat ',
        '&' => ' z@ne ',
        '+' => ' plus ',
    ],
    // Czech
    'cs' => [
        '=' => ' rovnat se ',
        '%' => ' procento ',
        '∑' => ' soucet ',
        '∆' => ' delta ',
        '∞' => ' nekonecno ',
        '♥' => ' laska ',
        '&' => ' a ',
        '+' => ' plus ',
    ],
    // Danish
    'da' => [
        '=' => ' Lige ',
        '%' => ' Prozent ',
        '∑' => ' sum ',
        '∆' => ' delta ',
        '∞' => ' uendelig ',
        '♥' => ' kaerlighed ',
        '&' => ' Og ',
        '+' => ' Plus ',
    ],
    // Polish
    'pl' => [
        '=' => ' rowny ',
        '%' => ' procent ',
        '∑' => ' suma ',
        '∆' => ' delta ',
        '∞' => ' nieskonczonosc ',
        '♥' => ' milosc ',
        '&' => ' i ',
        '+' => ' plus ',
    ],
    // Romanian
    'ro' => [
        '=' => ' egal ',
        '%' => ' la suta ',
        '∑' => ' suma ',
        '∆' => ' delta ',
        '∞' => ' infinit ',
        '♥' => ' dragoste ',
        '&' => ' si ',
        '+' => ' la care se adauga ',
    ],
    // Esperanto
    'eo' => [
        '=' => ' Egalaj ',
        '%' => ' Procento ',
        '∑' => ' sumo ',
        '∆' => ' delto ',
        '∞' => ' senfina ',
        '♥' => ' amo ',
        '&' => ' Kaj ',
        '+' => ' Pli ',
    ],
    // Estonian
    'et' => [
        '=' => ' Vordsed ',
        '%' => ' Protsenti ',
        '∑' => ' summa ',
        '∆' => ' o ',
        '∞' => ' loputut ',
        '♥' => ' armastus ',
        '&' => ' Ja ',
        '+' => ' Pluss ',
    ],
    // Latvian
    'lv' => [
        '=' => ' vienads ',
        '%' => ' procents ',
        '∑' => ' summa ',
        '∆' => ' delta ',
        '∞' => ' bezgaliba ',
        '♥' => ' milestiba ',
        '&' => ' un ',
        '+' => ' pluss ',
    ],
    // Lithuanian
    'lt' => [
        '=' => ' lygus ',
        '%' => ' procentu ',
        '∑' => ' suma ',
        '∆' => ' delta ',
        '∞' => ' begalybe ',
        '♥' => ' meile ',
        '&' => ' ir ',
        '+' => ' plius ',
    ],
    // Norwegian
    'no' => [
        '=' => ' Lik ',
        '%' => ' Prosent ',
        '∑' => ' sum ',
        '∆' => ' delta ',
        '∞' => ' uendelig ',
        '♥' => ' kjaerlighet ',
        '&' => ' Og ',
        '+' => ' Pluss ',
    ],
    // Vietnamese
    'vi' => [
        '=' => ' cong bang ',
        '%' => ' phan tram ',
        '∑' => ' tong so ',
        '∆' => ' dong bang ',
        '∞' => ' vo cuc ',
        '♥' => ' Yeu ',
        '&' => ' va ',
        '+' => ' them ',
    ],
    // Arabic
    'ar' => [
        '=' => ' mtsawy ',
        '%' => ' nsbh mywyh ',
        '∑' => ' mjmw\' ',
        '∆' => ' dlta ',
        '∞' => ' ma la nhayt ',
        '♥' => ' hb ',
        '&' => ' w ',
        '+' => ' zayd ',
    ],
    // Persian (Farsi)
    'fa' => [
        '=' => ' brabr ',
        '%' => ' dr sd ',
        '∑' => ' mjmw\' ',
        '∆' => ' dlta ',
        '∞' => ' by nhayt ',
        '♥' => ' \'shq ',
        '&' => ' w ',
        '+' => ' bh \'lawh ',
    ],
    // Serbian
    'sr' => [
        '=' => ' jednak ',
        '%' => ' procenat ',
        '∑' => ' zbir ',
        '∆' => ' delta ',
        '∞' => ' beskraj ',
        '♥' => ' lubav ',
        '&' => ' i ',
        '+' => ' vise ',
    ],
    // Serbian - Cyrillic
    'sr__cyr' => [
        '=' => ' jednak ',
        '%' => ' procenat ',
        '∑' => ' zbir ',
        '∆' => ' delta ',
        '∞' => ' beskraj ',
        '♥' => ' lubav ',
        '&' => ' i ',
        '+' => ' vise ',
    ],
    // Serbian - Latin
    'sr__lat' => [
        '=' => ' jednak ',
        '%' => ' procenat ',
        '∑' => ' zbir ',
        '∆' => ' delta ',
        '∞' => ' beskraj ',
        '♥' => ' lubav ',
        '&' => ' i ',
        '+' => ' vise ',
    ],
    // Azerbaijani
    'az' => [
        '=' => ' b@rab@r ',
        '%' => ' faiz ',
        '∑' => ' m@bl@g ',
        '∆' => ' delta ',
        '∞' => ' sonsuzluq ',
        '♥' => ' sevgi ',
        '&' => ' v@ ',
        '+' => ' plus ',
    ],
    // Slovak
    'sk' => [
        '=' => ' rovny ',
        '%' => ' percento ',
        '∑' => ' sucet ',
        '∆' => ' delta ',
        '∞' => ' infinity ',
        '♥' => ' milovat ',
        '&' => ' a ',
        '+' => ' viac ',
    ],
    // French
    'fr' => [
        '=' => ' Egal ',
        '%' => ' Pourcentage ',
        '∑' => ' somme ',
        '∆' => ' delta ',
        '∞' => ' infini ',
        '♥' => ' amour ',
        '&' => ' Et ',
        '+' => ' Plus ',
    ],
    // Austrian (French)
    'fr_at' => [
        '=' => ' Egal ',
        '%' => ' Pourcentage ',
        '∑' => ' somme ',
        '∆' => ' delta ',
        '∞' => ' infini ',
        '♥' => ' amour ',
        '&' => ' Et ',
        '+' => ' Plus ',
    ],
    // Switzerland (French)
    'fr_ch' => [
        '=' => ' Egal ',
        '%' => ' Pourcentage ',
        '∑' => ' somme ',
        '∆' => ' delta ',
        '∞' => ' infini ',
        '♥' => ' amour ',
        '&' => ' Et ',
        '+' => ' Plus ',
    ],
    // German
    'de' => [
        '=' => ' gleich ',
        '%' => ' Prozent ',
        '∑' => ' gesamt ',
        '∆' => ' Unterschied ',
        '∞' => ' undendlich ',
        '♥' => ' liebe ',
        '&' => ' und ',
        '+' => ' plus ',
    ],
    // Austrian (German)
    'de_at' => [
        '=' => ' gleich ',
        '%' => ' Prozent ',
        '∑' => ' gesamt ',
        '∆' => ' Unterschied ',
        '∞' => ' undendlich ',
        '♥' => ' liebe ',
        '&' => ' und ',
        '+' => ' plus ',
    ],
    // Switzerland (German)
    'de_ch' => [
        '=' => ' gleich ',
        '%' => ' Prozent ',
        '∑' => ' gesamt ',
        '∆' => ' Unterschied ',
        '∞' => ' undendlich ',
        '♥' => ' liebe ',
        '&' => ' und ',
        '+' => ' plus ',
    ],
    // Bengali (Bangla)
    'bn' => [
        '=' => ' Saman ',
        '%' => ' Satakora ',
        '∑' => ' Samasti ',
        '∆' => ' Badhip ',
        '∞' => ' Ananta ',
        '♥' => ' Valobasa ',
        '&' => ' Abong ',
        '+' => ' Songzojon ',
    ],
    // English
    'en' => [
        '=' => ' equal ',
        '%' => ' percent ',
        '∑' => ' sum ',
        '∆' => ' delta ',
        '∞' => ' infinity ',
        '♥' => ' love ',
        '&' => ' and ',
        '+' => ' plus ',
    ],
    // Currency
    //
    // url: https://en.wikipedia.org/wiki/Currency_symbol
    'currency' => [
        '€'  => ' Euro ',
        '$'  => ' Dollar ',
        '₢'  => ' cruzeiro ',
        '₣'  => ' French franc ',
        '£'  => ' pound ',
        '₤'  => ' lira ', // Italian
        '₶'  => ' livre tournois ',
        'ℳ'  => ' mark ',
        '₥'  => ' mill ',
        '₦'  => ' naira ',
        '₧'  => ' peseta ',
        '₨'  => ' rupee ',
        'රු' => ' rupee ', // Sri Lankan
        'ரூ' => ' rupee ', // Sri Lankan
        '௹'  => ' rupee ', // Tamil
        'रू' => ' rupee ', // Nepalese
        '₹'  => ' rupee ', // Indian
        '૱'  => ' rupee ', // Gujarat
        '₩'  => ' won ',
        '₪'  => ' new shequel ',
        '₸'  => ' tenge ',
        '₫'  => ' dong ',
        '֏'  => ' dram ',
        '₭'  => ' kip ',
        '₺'  => ' lira ', // Turkish
        '₼'  => ' manat ',
        '₮'  => ' tugrik ',
        '₯'  => ' drachma ',
        '₰'  => ' pfennig ',
        '₷'  => ' spesmilo ',
        '₱'  => ' peso ', // Philippine
        '﷼‎' => ' riyal ',
        '₲'  => ' guarani ',
        '₾'  => ' lari ',
        '₳'  => ' austral ',
        '₴'  => ' hryvnia ',
        '₽'  => ' ruble ',
        '₵'  => ' cedi ',
        '₡'  => ' colon ',
        '¢'  => ' cent ',
        '¥'  => ' yen ',
        '円'  => ' yen ',
        '৳'  => ' taka ',
        '元'  => ' yuan ',
        '﷼'  => ' riyal ',
        '៛'  => ' riel ',
        '₠'  => ' European Currency ',
        '¤'  => ' currency ',
        '฿'  => ' baht ',
        '؋'  => ' afghani ',
    ],
    // Temperature
    //
    // url: https://en.wikipedia.org/wiki/Conversion_of_units_of_temperature
    'temperature' => [
        '°De' => ' Delisle ',
        '°Re' => ' Reaumur ', // Réaumur
        '°Ro' => ' Romer ', // Rømer
        '°R'  => ' Rankine ',
        '°C'  => ' Celsius ',
        '°F'  => ' Fahrenheit ',
        '°N'  => ' Newton ',
    ],
    'latin_symbols' => [
        '=' => '=',
        '%' => '%',
        '∑' => '∑',
        '∆' => '∆',
        '∞' => '∞',
        '♥' => '♥',
        '&' => '&',
        '+' => '+',
        // ---
        '©' => ' (c) ',
        '®' => ' (r) ',
        '@' => ' (at) ',
        '№' => ' No. ',
        '℞' => ' Rx ',
        '［' => '[',
        '＼' => '\\',
        '］' => ']',
        '＾' => '^',
        '＿' => '_',
        '｀' => '`',
        '‐' => '-',
        '‑' => '-',
        '‒' => '-',
        '–' => '-',
        '−' => '-',
        '—' => '-',
        '―' => '-',
        '﹘' => '-',
        '│' => '|',
        '∖' => '\\',
        '∕' => '/',
        '⁄' => '/',
        '￩' => '<-',
        '￫' => '->',
        '￪' => '|',
        '￬' => '|',
        '⁅' => '[',
        '⁆' => ']',
        '⁎' => '*',
        '、' => ',',
        '。' => '.',
        '〈' => '<',
        '〉' => '>',
        '《' => '<<',
        '》' => '>>',
        '〔' => '[',
        '〕' => ']',
        '〘' => '[',
        '〙' => ']',
        '〚' => '[',
        '〛' => ']',
        '﹝' => '[',
        '﹞' => ']',
        '︹' => '[',
        '︺' => ']',
        '﹇' => '[',
        '﹈' => ']',
        '︐' => ',',
        '︑' => ',',
        '︒' => '.',
        '︓' => ':',
        '︔' => ';',
        '︕' => '!',
        '︖' => '?',
        '︙' => '...',
        '︰' => '..',
        '︵' => '(',
        '︶' => ')',
        '﹙' => '(',
        '﹚' => ')',
        '︷' => '{',
        '︸' => '}',
        '﹛' => '{',
        '﹜' => '}',
        '︽' => '<<',
        '︾' => '>>',
        '︿' => '<',
        '﹀' => '>',
        '×' => '*',
        '÷' => '/',
        '≪' => '<<',
        '≫' => '>>',
        '⦅' => '((',
        '⦆' => '))',
        '〇' => '0',
        '′' => '\'',
        '〝' => '"',
        '〞' => '"',
        '«' => '<<',
        '»' => '>>',
        '‘' => "'",
        '’' => "'",
        '‚' => ',',
        '‛' => "'",
        '“' => '"',
        '”' => '"',
        '„' => '"',
        '‟' => '"',
        '‹' => '<',
        '›' => '>',
        '․' => '.',
        '‥' => '..',
        '…' => '...',
        '″' => '"',
        '‴' => '\'\'\'',
        '‶' => '``',
        '‷' => '```',
        '‼' => '!!',
        '⁇' => '??',
        '⁈' => '?!',
        '⁉' => '!?',
        '⁗' => '````',
        '⩴' => '::=',
        '⩵' => '==',
        '⩶' => '===',
        '﹔' => ';',
        '﹕' => ':',
        '﹖' => '?',
        '﹗' => '!',
        '﹍' => '_',
        '﹎' => '_',
        '﹏' => '_',
        '﹐' => ',',
        '﹑' => ',',
        '﹒' => '.',
        '﹟' => '#',
        '﹠' => '&',
        '﹡' => '*',
        '﹢' => '+',
        '﹣' => '-',
        '﹤' => '<',
        '﹥' => '>',
        '﹦' => '=',
        '﹨' => '\\',
        '﹩' => '$',
        '﹪' => '%',
        '﹫' => '@',
        '！' => '!',
        '＂' => '"',
        '＃' => '#',
        '＄' => '$',
        '％' => '%',
        '＆' => '&',
        '＇' => '\'',
        '（' => '(',
        '）' => ')',
        '＊' => '*',
        '＋' => '+',
        '，' => ',',
        '－' => '-',
        '．' => '.',
        '／' => '/',
        '：' => ':',
        '；' => ';',
        '＜' => '<',
        '＝' => '=',
        '＞' => '>',
        '？' => '?',
        '＠' => '@',
        '｛' => '{',
        '｜' => '|',
        '｝' => '}',
        '～' => '~',
        '｟' => '((',
        '｠' => '))',
        '￢' => '!',
        '￣' => '-',
        '￤' => '|',
        '￭' => '#',
    ],
];
