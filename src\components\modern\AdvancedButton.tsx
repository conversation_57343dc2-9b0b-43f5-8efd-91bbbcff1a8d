import React, { useState, useRef } from 'react';
import { motion, useMotionValue, useSpring } from 'framer-motion';
import { modernAnimations, premiumEasing } from '@/lib/animations';
import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';

interface AdvancedButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'ghost' | 'neon' | 'glass' | 'gradient';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  rippleEffect?: boolean;
  magneticEffect?: boolean;
  glowEffect?: boolean;
  morphEffect?: boolean;
  className?: string;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
}

export const AdvancedButton: React.FC<AdvancedButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  rippleEffect = true,
  magneticEffect = false,
  glowEffect = false,
  morphEffect = false,
  className,
  onClick,
  type = 'button'
}) => {
  const [isPressed, setIsPressed] = useState(false);
  const [ripples, setRipples] = useState<Array<{ id: number; x: number; y: number }>>([]);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Motion values for magnetic effect
  const x = useMotionValue(0);
  const y = useMotionValue(0);
  const springX = useSpring(x, { damping: 20, stiffness: 300 });
  const springY = useSpring(y, { damping: 20, stiffness: 300 });

  const handleMouseMove = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (!magneticEffect || !buttonRef.current) return;

    const rect = buttonRef.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    const distance = Math.sqrt(
      Math.pow(event.clientX - centerX, 2) + Math.pow(event.clientY - centerY, 2)
    );
    
    if (distance < 100) {
      const strength = (100 - distance) / 100;
      x.set((event.clientX - centerX) * strength * 0.2);
      y.set((event.clientY - centerY) * strength * 0.2);
    }
  };

  const handleMouseLeave = () => {
    x.set(0);
    y.set(0);
  };

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled || loading) return;

    // Create ripple effect
    if (rippleEffect && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const rippleX = event.clientX - rect.left;
      const rippleY = event.clientY - rect.top;
      
      const newRipple = {
        id: Date.now(),
        x: rippleX,
        y: rippleY
      };
      
      setRipples(prev => [...prev, newRipple]);
      
      // Remove ripple after animation
      setTimeout(() => {
        setRipples(prev => prev.filter(r => r.id !== newRipple.id));
      }, 600);
    }

    onClick?.();
  };

  const getVariantStyles = () => {
    const baseStyles = 'relative overflow-hidden font-medium transition-all duration-300';
    
    switch (variant) {
      case 'primary':
        return cn(
          baseStyles,
          'bg-gradient-to-r from-blue-600 to-purple-600 text-white',
          'hover:from-blue-700 hover:to-purple-700',
          'focus:ring-4 focus:ring-blue-300',
          disabled && 'opacity-50 cursor-not-allowed'
        );
      case 'secondary':
        return cn(
          baseStyles,
          'bg-gray-100 text-gray-900 border border-gray-300',
          'hover:bg-gray-200',
          'focus:ring-4 focus:ring-gray-300',
          disabled && 'opacity-50 cursor-not-allowed'
        );
      case 'ghost':
        return cn(
          baseStyles,
          'bg-transparent text-gray-700 border border-transparent',
          'hover:bg-gray-100',
          'focus:ring-4 focus:ring-gray-300',
          disabled && 'opacity-50 cursor-not-allowed'
        );
      case 'neon':
        return cn(
          baseStyles,
          'bg-black text-green-400 border border-green-400',
          'hover:bg-green-400 hover:text-black',
          'focus:ring-4 focus:ring-green-300',
          disabled && 'opacity-50 cursor-not-allowed'
        );
      case 'glass':
        return cn(
          baseStyles,
          'bg-white/20 backdrop-blur-md text-white border border-white/30',
          'hover:bg-white/30',
          'focus:ring-4 focus:ring-white/20',
          disabled && 'opacity-50 cursor-not-allowed'
        );
      case 'gradient':
        return cn(
          baseStyles,
          'bg-gradient-to-r from-pink-500 via-red-500 to-yellow-500 text-white',
          'hover:from-pink-600 hover:via-red-600 hover:to-yellow-600',
          'focus:ring-4 focus:ring-pink-300',
          disabled && 'opacity-50 cursor-not-allowed'
        );
      default:
        return baseStyles;
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-1.5 text-sm rounded-md';
      case 'md':
        return 'px-4 py-2 text-base rounded-lg';
      case 'lg':
        return 'px-6 py-3 text-lg rounded-xl';
      case 'xl':
        return 'px-8 py-4 text-xl rounded-2xl';
      default:
        return 'px-4 py-2 text-base rounded-lg';
    }
  };

  return (
    <motion.button
      ref={buttonRef}
      type={type}
      className={cn(getVariantStyles(), getSizeStyles(), className)}
      disabled={disabled || loading}
      variants={morphEffect ? modernAnimations.magneticButton : undefined}
      initial="initial"
      whileHover={!disabled && !loading ? "hover" : undefined}
      whileTap={!disabled && !loading ? "tap" : undefined}
      style={{
        x: magneticEffect ? springX : 0,
        y: magneticEffect ? springY : 0
      }}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      onClick={handleClick}
    >
      {/* Glow effect */}
      {glowEffect && (
        <motion.div
          className="absolute inset-0 rounded-inherit"
          style={{
            background: variant === 'neon' 
              ? 'rgba(34, 197, 94, 0.3)' 
              : 'rgba(99, 102, 241, 0.3)',
            filter: 'blur(8px)',
            zIndex: -1
          }}
          initial={{ opacity: 0, scale: 0.8 }}
          whileHover={{ opacity: 1, scale: 1.1 }}
          transition={{ duration: 0.3 }}
        />
      )}

      {/* Ripple effects */}
      {ripples.map((ripple) => (
        <motion.span
          key={ripple.id}
          className="absolute rounded-full bg-white/30 pointer-events-none"
          style={{
            left: ripple.x - 10,
            top: ripple.y - 10,
            width: 20,
            height: 20
          }}
          initial={{ scale: 0, opacity: 0.8 }}
          animate={{ scale: 4, opacity: 0 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
        />
      ))}

      {/* Button content */}
      <span className="relative flex items-center justify-center gap-2">
        {loading && (
          <Loader2 className="w-4 h-4 animate-spin" />
        )}
        {!loading && icon && iconPosition === 'left' && (
          <span className="flex-shrink-0">{icon}</span>
        )}
        <span>{children}</span>
        {!loading && icon && iconPosition === 'right' && (
          <span className="flex-shrink-0">{icon}</span>
        )}
      </span>

      {/* Shine effect */}
      {variant !== 'ghost' && (
        <motion.div
          className="absolute inset-0 rounded-inherit pointer-events-none overflow-hidden"
          initial={{ opacity: 0 }}
          whileHover={{ opacity: 1 }}
        >
          <motion.div
            className="absolute -top-2 -left-2 w-4 h-full bg-gradient-to-r from-transparent via-white to-transparent opacity-20"
            style={{ transform: 'rotate(45deg)' }}
            animate={{
              x: [-100, 200],
              transition: {
                duration: 1,
                ease: "easeInOut",
                repeat: Infinity,
                repeatDelay: 2
              }
            }}
          />
        </motion.div>
      )}
    </motion.button>
  );
};

// Preset button variants
export const NeonButton: React.FC<Omit<AdvancedButtonProps, 'variant'>> = (props) => (
  <AdvancedButton {...props} variant="neon" glowEffect magneticEffect />
);

export const GlassButton: React.FC<Omit<AdvancedButtonProps, 'variant'>> = (props) => (
  <AdvancedButton {...props} variant="glass" glowEffect />
);

export const GradientButton: React.FC<Omit<AdvancedButtonProps, 'variant'>> = (props) => (
  <AdvancedButton {...props} variant="gradient" morphEffect />
);
