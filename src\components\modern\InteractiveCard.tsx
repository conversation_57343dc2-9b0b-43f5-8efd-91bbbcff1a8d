import React, { useState, useRef } from 'react';
import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';
import { modernAnimations, premiumEasing } from '@/lib/animations';
import { cn } from '@/lib/utils';

interface InteractiveCardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'glass' | 'neon' | 'minimal';
  interactive?: boolean;
  glowEffect?: boolean;
  tiltEffect?: boolean;
  magneticEffect?: boolean;
  onClick?: () => void;
}

export const InteractiveCard: React.FC<InteractiveCardProps> = ({
  children,
  className,
  variant = 'default',
  interactive = true,
  glowEffect = false,
  tiltEffect = false,
  magneticEffect = false,
  onClick
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  // Motion values for advanced interactions
  const x = useMotionValue(0);
  const y = useMotionValue(0);
  const rotateX = useSpring(useTransform(y, [-100, 100], [30, -30]), {
    damping: 30,
    stiffness: 400
  });
  const rotateY = useSpring(useTransform(x, [-100, 100], [-30, 30]), {
    damping: 30,
    stiffness: 400
  });

  // Magnetic effect
  const handleMouseMove = (event: React.MouseEvent<HTMLDivElement>) => {
    if (!cardRef.current || !interactive) return;

    const rect = cardRef.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    if (tiltEffect) {
      x.set(event.clientX - centerX);
      y.set(event.clientY - centerY);
    }

    if (magneticEffect) {
      const distance = Math.sqrt(
        Math.pow(event.clientX - centerX, 2) + Math.pow(event.clientY - centerY, 2)
      );
      
      if (distance < 100) {
        const strength = (100 - distance) / 100;
        x.set((event.clientX - centerX) * strength * 0.3);
        y.set((event.clientY - centerY) * strength * 0.3);
      }
    }
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    x.set(0);
    y.set(0);
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'glass':
        return {
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          boxShadow: isHovered 
            ? '0 25px 50px -12px rgba(0, 0, 0, 0.25)' 
            : '0 8px 32px 0 rgba(31, 38, 135, 0.37)'
        };
      case 'neon':
        return {
          background: 'rgba(0, 0, 0, 0.8)',
          border: '1px solid #00ff88',
          boxShadow: isHovered
            ? '0 0 30px rgba(0, 255, 136, 0.6), inset 0 0 30px rgba(0, 255, 136, 0.1)'
            : '0 0 15px rgba(0, 255, 136, 0.3)'
        };
      case 'minimal':
        return {
          background: '#ffffff',
          border: '1px solid #e5e7eb',
          boxShadow: isHovered
            ? '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
            : '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
        };
      default:
        return {
          background: '#ffffff',
          border: '1px solid #e5e7eb',
          boxShadow: isHovered
            ? '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
            : '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        };
    }
  };

  return (
    <motion.div
      ref={cardRef}
      className={cn(
        'relative rounded-xl p-6 cursor-pointer transition-all duration-300',
        interactive && 'hover:scale-105',
        className
      )}
      style={{
        ...getVariantStyles(),
        rotateX: tiltEffect ? rotateX : 0,
        rotateY: tiltEffect ? rotateY : 0,
        x: magneticEffect ? x : 0,
        y: magneticEffect ? y : 0,
        transformStyle: 'preserve-3d'
      }}
      variants={modernAnimations.liquidCard}
      initial="initial"
      animate="animate"
      whileHover={interactive ? "hover" : undefined}
      whileTap={interactive ? "tap" : undefined}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={handleMouseLeave}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      onClick={onClick}
    >
      {/* Glow effect overlay */}
      {glowEffect && isHovered && (
        <motion.div
          className="absolute inset-0 rounded-xl opacity-50"
          style={{
            background: variant === 'neon' 
              ? 'radial-gradient(circle at center, rgba(0, 255, 136, 0.3) 0%, transparent 70%)'
              : 'radial-gradient(circle at center, rgba(99, 102, 241, 0.3) 0%, transparent 70%)',
            filter: 'blur(20px)',
            zIndex: -1
          }}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 0.5, scale: 1.2 }}
          exit={{ opacity: 0, scale: 0.8 }}
          transition={{ duration: 0.3 }}
        />
      )}

      {/* Ripple effect on click */}
      {isPressed && (
        <motion.div
          className="absolute inset-0 rounded-xl pointer-events-none"
          style={{
            background: 'radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%)'
          }}
          variants={modernAnimations.ripple}
          initial="initial"
          animate="animate"
        />
      )}

      {/* Content with 3D transform */}
      <div
        style={{
          transform: tiltEffect ? 'translateZ(50px)' : 'none'
        }}
      >
        {children}
      </div>

      {/* Shine effect */}
      {isHovered && variant !== 'minimal' && (
        <motion.div
          className="absolute inset-0 rounded-xl pointer-events-none overflow-hidden"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            className="absolute -top-2 -left-2 w-4 h-full bg-gradient-to-r from-transparent via-white to-transparent opacity-30"
            style={{ transform: 'rotate(45deg)' }}
            animate={{
              x: [-100, 400],
              transition: {
                duration: 1.5,
                ease: "easeInOut",
                repeat: Infinity,
                repeatDelay: 3
              }
            }}
          />
        </motion.div>
      )}
    </motion.div>
  );
};

// Preset card variants for quick use
export const GlassCard: React.FC<Omit<InteractiveCardProps, 'variant'>> = (props) => (
  <InteractiveCard {...props} variant="glass" glowEffect tiltEffect />
);

export const NeonCard: React.FC<Omit<InteractiveCardProps, 'variant'>> = (props) => (
  <InteractiveCard {...props} variant="neon" glowEffect magneticEffect />
);

export const MinimalCard: React.FC<Omit<InteractiveCardProps, 'variant'>> = (props) => (
  <InteractiveCard {...props} variant="minimal" />
);
