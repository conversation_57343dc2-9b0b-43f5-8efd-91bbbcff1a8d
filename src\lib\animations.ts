import { Variants } from 'framer-motion';

// Professional animation variants for business applications
export const professionalAnimations = {
  // Subtle page transitions
  pageTransition: {
    initial: {
      opacity: 0,
      y: 8
    },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    },
    exit: {
      opacity: 0,
      y: -8,
      transition: { duration: 0.2 }
    },
  },

  // Liquid morphing cards
  liquidCard: {
    initial: {
      scale: 0.8,
      opacity: 0,
      borderRadius: "50%",
      rotate: -10
    },
    animate: {
      scale: 1,
      opacity: 1,
      borderRadius: "12px",
      rotate: 0,
      transition: {
        type: "spring",
        damping: 20,
        stiffness: 300,
        borderRadius: { duration: 0.6, ease: [0.25, 0.46, 0.45, 0.94] }
      }
    },
    hover: {
      scale: 1.05,
      y: -8,
      rotateY: 5,
      boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
      transition: { duration: 0.3 }
    },
    tap: { scale: 0.95 }
  },

  // Magnetic button effects
  magneticButton: {
    initial: { scale: 1 },
    hover: {
      scale: 1.1,
      boxShadow: "0 0 30px rgba(99, 102, 241, 0.4)",
      background: "linear-gradient(45deg, #6366f1, #8b5cf6)",
      transition: {
        duration: 0.3,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    },
    tap: {
      scale: 0.9,
      boxShadow: "0 0 15px rgba(99, 102, 241, 0.6)"
    }
  },

  // Floating elements with parallax
  floatingElement: {
    animate: {
      y: [0, -20, 0],
      rotate: [0, 5, 0],
      scale: [1, 1.02, 1],
      transition: {
        duration: 6,
        repeat: Infinity,
        ease: "easeInOut",
        times: [0, 0.5, 1]
      }
    }
  },

  // Glassmorphism reveal
  glassReveal: {
    initial: {
      opacity: 0,
      backdropFilter: "blur(0px)",
      background: "rgba(255, 255, 255, 0)"
    },
    animate: {
      opacity: 1,
      backdropFilter: "blur(20px)",
      background: "rgba(255, 255, 255, 0.1)",
      transition: {
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  },

  // Staggered grid with wave effect
  waveGrid: {
    animate: {
      transition: {
        staggerChildren: 0.05,
        delayChildren: 0.1,
        staggerDirection: 1
      }
    }
  },

  waveGridItem: {
    initial: {
      opacity: 0,
      y: 60,
      rotateX: -90,
      scale: 0.5
    },
    animate: {
      opacity: 1,
      y: 0,
      rotateX: 0,
      scale: 1,
      transition: {
        type: "spring",
        damping: 25,
        stiffness: 400,
        mass: 0.8
      }
    }
  },

  // Morphing sidebar
  morphingSidebar: {
    collapsed: {
      width: 80,
      borderRadius: "0 24px 24px 0",
      transition: {
        duration: 0.6,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    },
    expanded: {
      width: 280,
      borderRadius: "0 12px 12px 0",
      transition: {
        duration: 0.6,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  },

  // Particle-like loading
  particleLoad: {
    animate: {
      scale: [0, 1.2, 0],
      opacity: [0, 1, 0],
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut",
        times: [0, 0.5, 1]
      }
    }
  },

  // Advanced modal with backdrop blur
  advancedModal: {
    backdrop: {
      initial: {
        opacity: 0,
        backdropFilter: "blur(0px)"
      },
      animate: {
        opacity: 1,
        backdropFilter: "blur(8px)",
        transition: { duration: 0.4 }
      },
      exit: {
        opacity: 0,
        backdropFilter: "blur(0px)",
        transition: { duration: 0.3 }
      }
    },
    content: {
      initial: {
        opacity: 0,
        scale: 0.7,
        y: 100,
        rotateX: -15
      },
      animate: {
        opacity: 1,
        scale: 1,
        y: 0,
        rotateX: 0,
        transition: {
          type: "spring",
          damping: 25,
          stiffness: 300,
          delay: 0.1
        }
      },
      exit: {
        opacity: 0,
        scale: 0.7,
        y: 100,
        rotateX: 15,
        transition: { duration: 0.3 }
      }
    }
  },

  // Elastic search bar
  elasticSearch: {
    initial: { width: 40, opacity: 0.7 },
    focus: {
      width: 300,
      opacity: 1,
      boxShadow: "0 0 0 3px rgba(99, 102, 241, 0.1)",
      transition: {
        type: "spring",
        damping: 20,
        stiffness: 300
      }
    },
    blur: {
      width: 40,
      opacity: 0.7,
      boxShadow: "0 0 0 0px rgba(99, 102, 241, 0)",
      transition: { duration: 0.4 }
    }
  },

  // Ripple effect
  ripple: {
    initial: { scale: 0, opacity: 0.8 },
    animate: {
      scale: 4,
      opacity: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  },

  // Typewriter effect
  typewriter: {
    initial: { width: 0 },
    animate: {
      width: "100%",
      transition: {
        duration: 2,
        ease: "easeInOut"
      }
    }
  }
};

// Advanced easing curves for premium animations
export const premiumEasing = {
  smooth: [0.25, 0.46, 0.45, 0.94],
  snappy: [0.68, -0.55, 0.265, 1.55],
  elastic: [0.175, 0.885, 0.32, 1.275],
  dramatic: [0.95, 0.05, 0.795, 0.035],
  gentle: [0.4, 0, 0.2, 1],
  bounce: [0.68, -0.6, 0.32, 1.6]
};

// Utility for creating complex spring animations
export const createAdvancedSpring = (
  damping = 25,
  stiffness = 300,
  mass = 1,
  velocity = 0
) => ({
  type: "spring" as const,
  damping,
  stiffness,
  mass,
  velocity
});

// Gesture-based animations
export const gestureAnimations = {
  swipeLeft: { x: -100, opacity: 0 },
  swipeRight: { x: 100, opacity: 0 },
  swipeUp: { y: -100, opacity: 0 },
  swipeDown: { y: 100, opacity: 0 }
};

// Micro-interaction animations
export const microAnimations = {
  buttonHover: {
    scale: 1.05,
    transition: { duration: 0.2, ease: premiumEasing.smooth }
  },
  iconSpin: {
    rotate: 360,
    transition: { duration: 0.5, ease: "linear" }
  },
  heartbeat: {
    scale: [1, 1.2, 1],
    transition: { duration: 0.6, ease: premiumEasing.elastic }
  },
  shake: {
    x: [0, -10, 10, -10, 10, 0],
    transition: { duration: 0.5 }
  }
};
