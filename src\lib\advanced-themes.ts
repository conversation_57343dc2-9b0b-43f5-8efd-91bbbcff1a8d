// Advanced Theme System with Multiple Creative Themes
export interface AdvancedTheme {
  id: string;
  name: string;
  description: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: {
      primary: string;
      secondary: string;
      muted: string;
    };
    semantic: {
      success: string;
      warning: string;
      error: string;
      info: string;
    };
    gradients: {
      primary: string;
      secondary: string;
      accent: string;
    };
  };
  effects: {
    glassmorphism: {
      background: string;
      backdropFilter: string;
      border: string;
    };
    shadows: {
      sm: string;
      md: string;
      lg: string;
      xl: string;
      glow: string;
    };
    animations: {
      duration: string;
      easing: string;
    };
  };
  typography: {
    fontFamily: string;
    headingWeight: number;
    bodyWeight: number;
  };
}

export const advancedThemes: Record<string, AdvancedTheme> = {
  // Cyberpunk Neon Theme
  cyberpunk: {
    id: 'cyberpunk',
    name: 'Cyberpunk Neon',
    description: 'Futuristic dark theme with neon accents and glowing effects',
    colors: {
      primary: '#00ff88',
      secondary: '#ff0080',
      accent: '#00d4ff',
      background: '#0a0a0f',
      surface: '#1a1a2e',
      text: {
        primary: '#ffffff',
        secondary: '#b3b3b3',
        muted: '#666666'
      },
      semantic: {
        success: '#00ff88',
        warning: '#ffaa00',
        error: '#ff0080',
        info: '#00d4ff'
      },
      gradients: {
        primary: 'linear-gradient(135deg, #00ff88 0%, #00d4ff 100%)',
        secondary: 'linear-gradient(135deg, #ff0080 0%, #ff6b35 100%)',
        accent: 'linear-gradient(135deg, #00d4ff 0%, #8b5cf6 100%)'
      }
    },
    effects: {
      glassmorphism: {
        background: 'rgba(26, 26, 46, 0.8)',
        backdropFilter: 'blur(20px) saturate(180%)',
        border: '1px solid rgba(0, 255, 136, 0.2)'
      },
      shadows: {
        sm: '0 2px 4px rgba(0, 255, 136, 0.1)',
        md: '0 4px 12px rgba(0, 255, 136, 0.15)',
        lg: '0 8px 24px rgba(0, 255, 136, 0.2)',
        xl: '0 16px 48px rgba(0, 255, 136, 0.25)',
        glow: '0 0 20px rgba(0, 255, 136, 0.5)'
      },
      animations: {
        duration: '0.3s',
        easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
      }
    },
    typography: {
      fontFamily: 'JetBrains Mono, monospace',
      headingWeight: 700,
      bodyWeight: 400
    }
  },

  // Glassmorphism Light Theme
  glassmorphism: {
    id: 'glassmorphism',
    name: 'Glassmorphism Light',
    description: 'Modern glass-like interface with soft transparency effects',
    colors: {
      primary: '#6366f1',
      secondary: '#8b5cf6',
      accent: '#06b6d4',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      surface: 'rgba(255, 255, 255, 0.25)',
      text: {
        primary: '#1f2937',
        secondary: '#4b5563',
        muted: '#9ca3af'
      },
      semantic: {
        success: '#10b981',
        warning: '#f59e0b',
        error: '#ef4444',
        info: '#3b82f6'
      },
      gradients: {
        primary: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
        secondary: 'linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%)',
        accent: 'linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%)'
      }
    },
    effects: {
      glassmorphism: {
        background: 'rgba(255, 255, 255, 0.25)',
        backdropFilter: 'blur(20px) saturate(180%)',
        border: '1px solid rgba(255, 255, 255, 0.18)'
      },
      shadows: {
        sm: '0 2px 4px rgba(0, 0, 0, 0.05)',
        md: '0 4px 12px rgba(0, 0, 0, 0.1)',
        lg: '0 8px 24px rgba(0, 0, 0, 0.15)',
        xl: '0 16px 48px rgba(0, 0, 0, 0.2)',
        glow: '0 0 20px rgba(99, 102, 241, 0.3)'
      },
      animations: {
        duration: '0.4s',
        easing: 'cubic-bezier(0.16, 1, 0.3, 1)'
      }
    },
    typography: {
      fontFamily: 'Inter, sans-serif',
      headingWeight: 600,
      bodyWeight: 400
    }
  },

  // Retro Wave Theme
  retrowave: {
    id: 'retrowave',
    name: 'Retro Wave',
    description: '80s inspired theme with vibrant gradients and retro aesthetics',
    colors: {
      primary: '#ff6b9d',
      secondary: '#c44569',
      accent: '#f8b500',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      surface: '#2d1b69',
      text: {
        primary: '#ffffff',
        secondary: '#e0e0e0',
        muted: '#a0a0a0'
      },
      semantic: {
        success: '#00d4aa',
        warning: '#f8b500',
        error: '#ff6b9d',
        info: '#00d4ff'
      },
      gradients: {
        primary: 'linear-gradient(135deg, #ff6b9d 0%, #c44569 100%)',
        secondary: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        accent: 'linear-gradient(135deg, #f8b500 0%, #feca57 100%)'
      }
    },
    effects: {
      glassmorphism: {
        background: 'rgba(45, 27, 105, 0.8)',
        backdropFilter: 'blur(15px) saturate(200%)',
        border: '1px solid rgba(255, 107, 157, 0.3)'
      },
      shadows: {
        sm: '0 2px 4px rgba(255, 107, 157, 0.1)',
        md: '0 4px 12px rgba(255, 107, 157, 0.2)',
        lg: '0 8px 24px rgba(255, 107, 157, 0.3)',
        xl: '0 16px 48px rgba(255, 107, 157, 0.4)',
        glow: '0 0 30px rgba(255, 107, 157, 0.6)'
      },
      animations: {
        duration: '0.5s',
        easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
      }
    },
    typography: {
      fontFamily: 'Orbitron, monospace',
      headingWeight: 800,
      bodyWeight: 400
    }
  },

  // Minimalist Pro Theme
  minimalist: {
    id: 'minimalist',
    name: 'Minimalist Pro',
    description: 'Clean, professional theme focused on content and usability',
    colors: {
      primary: '#000000',
      secondary: '#333333',
      accent: '#0066cc',
      background: '#ffffff',
      surface: '#f8f9fa',
      text: {
        primary: '#000000',
        secondary: '#333333',
        muted: '#666666'
      },
      semantic: {
        success: '#28a745',
        warning: '#ffc107',
        error: '#dc3545',
        info: '#17a2b8'
      },
      gradients: {
        primary: 'linear-gradient(135deg, #000000 0%, #333333 100%)',
        secondary: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
        accent: 'linear-gradient(135deg, #0066cc 0%, #004499 100%)'
      }
    },
    effects: {
      glassmorphism: {
        background: 'rgba(248, 249, 250, 0.9)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(0, 0, 0, 0.1)'
      },
      shadows: {
        sm: '0 1px 3px rgba(0, 0, 0, 0.1)',
        md: '0 4px 6px rgba(0, 0, 0, 0.1)',
        lg: '0 10px 15px rgba(0, 0, 0, 0.1)',
        xl: '0 20px 25px rgba(0, 0, 0, 0.1)',
        glow: '0 0 0 3px rgba(0, 102, 204, 0.1)'
      },
      animations: {
        duration: '0.2s',
        easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
      }
    },
    typography: {
      fontFamily: 'Inter, sans-serif',
      headingWeight: 500,
      bodyWeight: 400
    }
  }
};

// Theme utility functions
export function getThemeCSS(theme: AdvancedTheme): string {
  return `
    :root {
      --theme-primary: ${theme.colors.primary};
      --theme-secondary: ${theme.colors.secondary};
      --theme-accent: ${theme.colors.accent};
      --theme-background: ${theme.colors.background};
      --theme-surface: ${theme.colors.surface};
      --theme-text-primary: ${theme.colors.text.primary};
      --theme-text-secondary: ${theme.colors.text.secondary};
      --theme-text-muted: ${theme.colors.text.muted};
      --theme-gradient-primary: ${theme.colors.gradients.primary};
      --theme-gradient-secondary: ${theme.colors.gradients.secondary};
      --theme-gradient-accent: ${theme.colors.gradients.accent};
      --theme-glass-bg: ${theme.effects.glassmorphism.background};
      --theme-glass-backdrop: ${theme.effects.glassmorphism.backdropFilter};
      --theme-glass-border: ${theme.effects.glassmorphism.border};
      --theme-shadow-glow: ${theme.effects.shadows.glow};
      --theme-font-family: ${theme.typography.fontFamily};
    }
  `;
}

export function applyTheme(themeId: string): void {
  const theme = advancedThemes[themeId];
  if (!theme) return;

  const styleElement = document.getElementById('advanced-theme-styles') || document.createElement('style');
  styleElement.id = 'advanced-theme-styles';
  styleElement.textContent = getThemeCSS(theme);
  
  if (!document.getElementById('advanced-theme-styles')) {
    document.head.appendChild(styleElement);
  }
}
