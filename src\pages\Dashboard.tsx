
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { InteractiveCard, GlassCard } from "@/components/modern/InteractiveCard";
import { AdvancedButton, NeonButton, GradientButton } from "@/components/modern/AdvancedButton";
import { modernAnimations, premiumEasing } from '@/lib/animations';
import {
  BarChartBig,
  MessageSquare,
  Users,
  BarChart,
  TrendingUp,
  Activity,
  Zap,
  Globe,
  Sparkles,
  ArrowUpRight
} from "lucide-react";

const Dashboard = () => {
  const [selectedMetric, setSelectedMetric] = useState<string | null>(null);
  const [animatedValues, setAnimatedValues] = useState({
    conversations: 0,
    users: 0,
    responseTime: 0,
    satisfaction: 0
  });

  // Animate numbers on mount
  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedValues({
        conversations: 2853,
        users: 1324,
        responseTime: 1.2,
        satisfaction: 89
      });
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const metrics = [
    {
      id: 'conversations',
      title: 'Total Conversations',
      value: animatedValues.conversations.toLocaleString(),
      change: '+12%',
      changeType: 'positive',
      icon: MessageSquare,
      color: 'from-blue-500 to-cyan-500',
      description: 'from last month'
    },
    {
      id: 'users',
      title: 'Active Users',
      value: animatedValues.users.toLocaleString(),
      change: '+5.2%',
      changeType: 'positive',
      icon: Users,
      color: 'from-green-500 to-emerald-500',
      description: 'from last month'
    },
    {
      id: 'responseTime',
      title: 'Response Time',
      value: `${animatedValues.responseTime}s`,
      change: '-0.5s',
      changeType: 'positive',
      icon: Zap,
      color: 'from-yellow-500 to-orange-500',
      description: 'improvement'
    },
    {
      id: 'satisfaction',
      title: 'User Satisfaction',
      value: `${animatedValues.satisfaction}%`,
      change: '+2%',
      changeType: 'positive',
      icon: BarChartBig,
      color: 'from-purple-500 to-pink-500',
      description: 'user satisfaction'
    }
  ];

  return (
    <motion.div
      className="space-y-8 p-6"
      variants={modernAnimations.pageTransition3D}
      initial="initial"
      animate="animate"
      exit="exit"
    >
      {/* Header Section with Floating Elements */}
      <motion.div
        className="relative"
        variants={modernAnimations.waveGrid}
        initial="initial"
        animate="animate"
      >
        <motion.div
          className="absolute -top-4 -right-4 w-20 h-20 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-20 blur-xl"
          variants={modernAnimations.floatingElement}
          animate="animate"
        />
        <motion.div
          className="absolute -bottom-2 -left-2 w-16 h-16 bg-gradient-to-r from-green-400 to-blue-500 rounded-full opacity-20 blur-xl"
          variants={modernAnimations.floatingElement}
          animate="animate"
          style={{ animationDelay: '1s' }}
        />

        <motion.div variants={modernAnimations.waveGridItem}>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
            Dashboard
          </h1>
          <p className="text-gray-600 mt-2 text-lg">
            Welcome to your advanced chat administration dashboard
          </p>
        </motion.div>
      </motion.div>

      {/* Metrics Grid with Advanced Cards */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        variants={modernAnimations.waveGrid}
        initial="initial"
        animate="animate"
      >
        {metrics.map((metric, index) => {
          const Icon = metric.icon;
          return (
            <motion.div
              key={metric.id}
              variants={modernAnimations.waveGridItem}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <InteractiveCard
                variant="glass"
                glowEffect
                tiltEffect
                className={`relative overflow-hidden cursor-pointer transition-all duration-500 ${selectedMetric === metric.id ? 'ring-2 ring-blue-400' : ''
                  }`}
                onClick={() => setSelectedMetric(selectedMetric === metric.id ? null : metric.id)}
              >
                {/* Background Gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${metric.color} opacity-5`} />

                {/* Header */}
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-sm font-medium text-gray-600">{metric.title}</h3>
                  <motion.div
                    className={`p-2 rounded-lg bg-gradient-to-r ${metric.color} text-white`}
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <Icon className="w-4 h-4" />
                  </motion.div>
                </div>

                {/* Value */}
                <motion.div
                  className="text-3xl font-bold text-gray-900 mb-2"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 + 0.5, duration: 0.6 }}
                >
                  {metric.value}
                </motion.div>

                {/* Change Indicator */}
                <div className="flex items-center gap-2">
                  <motion.span
                    className={`inline-flex items-center gap-1 text-sm font-medium ${metric.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                      }`}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 + 0.7 }}
                  >
                    <ArrowUpRight className="w-3 h-3" />
                    {metric.change}
                  </motion.span>
                  <span className="text-xs text-gray-500">{metric.description}</span>
                </div>

                {/* Sparkle Effect */}
                {selectedMetric === metric.id && (
                  <motion.div
                    className="absolute top-2 right-2"
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0 }}
                  >
                    <Sparkles className="w-4 h-4 text-yellow-400" />
                  </motion.div>
                )}
              </InteractiveCard>
            </motion.div>
          );
        })}
      </motion.div>

      {/* Advanced Content Sections */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
        {/* Recent Conversations with Advanced Styling */}
        <motion.div
          variants={modernAnimations.waveGridItem}
          initial="initial"
          animate="animate"
          transition={{ delay: 0.8 }}
        >
          <GlassCard className="h-full">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-gray-900">Recent Conversations</h3>
              <AdvancedButton size="sm" variant="ghost">
                View All
              </AdvancedButton>
            </div>

            <div className="space-y-4">
              {[1, 2, 3, 4, 5].map((i) => (
                <motion.div
                  key={i}
                  className="flex items-center justify-between p-3 rounded-lg hover:bg-white/50 transition-all duration-300 cursor-pointer group"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.9 + i * 0.1 }}
                  whileHover={{ scale: 1.02, x: 5 }}
                >
                  <div className="flex items-center space-x-3">
                    <motion.div
                      className="h-10 w-10 rounded-full bg-gradient-to-r from-blue-400 to-purple-500 flex items-center justify-center text-white font-medium"
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.5 }}
                    >
                      U{i}
                    </motion.div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">User #{i}</p>
                      <p className="text-xs text-gray-500">Last active: {15 - i} minutes ago</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                      {i} message{i > 1 ? 's' : ''}
                    </span>
                    <motion.div
                      className="w-2 h-2 bg-green-400 rounded-full"
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    />
                  </div>
                </motion.div>
              ))}
            </div>
          </GlassCard>
        </motion.div>

        {/* Interactive Activity Chart */}
        <motion.div
          variants={modernAnimations.waveGridItem}
          initial="initial"
          animate="animate"
          transition={{ delay: 1.0 }}
        >
          <GlassCard className="h-full">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-gray-900">Activity Overview</h3>
              <div className="flex gap-2">
                <NeonButton size="sm">Live</NeonButton>
                <GradientButton size="sm">Export</GradientButton>
              </div>
            </div>

            <div className="h-[300px] relative overflow-hidden rounded-lg bg-gradient-to-br from-blue-50 to-purple-50">
              {/* Animated Background Pattern */}
              <motion.div
                className="absolute inset-0 opacity-10"
                style={{
                  backgroundImage: `radial-gradient(circle at 25% 25%, #6366f1 0%, transparent 50%),
                                   radial-gradient(circle at 75% 75%, #8b5cf6 0%, transparent 50%)`
                }}
                animate={{
                  backgroundPosition: ['0% 0%', '100% 100%'],
                }}
                transition={{
                  duration: 20,
                  repeat: Infinity,
                  repeatType: 'reverse'
                }}
              />

              {/* Placeholder Chart with Animation */}
              <div className="flex items-center justify-center h-full">
                <motion.div
                  className="text-center"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 1.2 }}
                >
                  <motion.div
                    className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                  >
                    <Activity className="w-8 h-8 text-white" />
                  </motion.div>
                  <p className="text-gray-600 font-medium">Interactive Chart</p>
                  <p className="text-sm text-gray-500">Real-time analytics visualization</p>
                </motion.div>
              </div>

              {/* Floating Data Points */}
              {[...Array(6)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-3 h-3 bg-blue-400 rounded-full opacity-60"
                  style={{
                    left: `${20 + i * 12}%`,
                    top: `${30 + Math.sin(i) * 20}%`
                  }}
                  animate={{
                    y: [0, -10, 0],
                    opacity: [0.6, 1, 0.6]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    delay: i * 0.2
                  }}
                />
              ))}
            </div>
          </GlassCard>
        </motion.div>
      </div>

      {/* Quick Actions Section */}
      <motion.div
        className="flex flex-wrap gap-4 justify-center"
        variants={modernAnimations.waveGrid}
        initial="initial"
        animate="animate"
        transition={{ delay: 1.4 }}
      >
        <GradientButton
          size="lg"
          magneticEffect
          glowEffect
          icon={<MessageSquare className="w-5 h-5" />}
        >
          Start New Chat
        </GradientButton>
        <NeonButton
          size="lg"
          magneticEffect
          icon={<BarChartBig className="w-5 h-5" />}
        >
          View Analytics
        </NeonButton>
        <AdvancedButton
          size="lg"
          variant="glass"
          glowEffect
          icon={<Users className="w-5 h-5" />}
        >
          Manage Users
        </AdvancedButton>
      </motion.div>
    </motion.div>
  );
};

export default Dashboard;
